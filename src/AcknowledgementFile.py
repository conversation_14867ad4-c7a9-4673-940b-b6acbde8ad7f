import struct
import pandas as pd
import os
import binascii
from datetime import datetime

# 切换到项目根目录
os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 读取二进制文件内容
with open("src/data/AcknowledgementFile_DAY1", "rb") as f:
    content = f.read()


# 辅助函数：读取 n 个字节并偏移
def read_bytes(data, offset, length):
    return data[offset:offset + length], offset + length


# 辅助函数：将 bytes 转为十六进制字符串
def to_hex_str(b):
    return b.hex().upper()


def hex_to_ascii(hex_string):
    """将十六进制字符串转换为其ASCII表示。"""
    try:
        return binascii.unhexlify(hex_string).decode('ascii')
    except binascii.Error:
        return f"Invalid Hex: {hex_string}"
    except UnicodeDecodeError:
        return f"Decode Error: {hex_string}"


def hex_to_int(hex_string):
    """将十六进制字符串转换为其整数表示。"""
    try:
        return int(hex_string, 16)
    except ValueError:
        return f"Invalid Hex for Int: {hex_string}"


def parse_bitmap(bitmap1: str, bit1: str = "") -> list:
    def hex_to_bits(hex_str):
        return bin(int(hex_str, 16))[2:].zfill(len(hex_str) * 4)

    fields = []

    # 处理 bitmap1（字段 1 到 64）
    bits1 = hex_to_bits(bitmap1)
    for i, bit in enumerate(bits1):
        if bit == '1':
            fields.append(i + 1)

    # 处理 bit1（字段 65 到 128）
    if bit1 and len(bit1) == 16:  # 8 字节 = 16 hex 字符
        bits2 = hex_to_bits(bit1)
        for i, bit in enumerate(bits2):
            if bit == '1':
                fields.append(i + 65)

    return sorted(fields)


# 初始化变量
offset = 0
records = []
record_index = 1

# 用于映射 Function Code 到描述
function_code_map = {
    '200': 'Presentment',
    '205': 'Representment Full',
    '280': 'Representment Partial',
    '450': 'First Chargeback Full',
    '451': 'Second Chargeback Full',
    '453': 'First Chargeback Partial',
    '454': 'Second Chargeback Partial',
    '500': 'Reconciliation Data',
    '570': 'Acknowledgement File',
    '571': 'Settlement Summary Information',
    '603': 'Retrieval Request',
    '695': 'Addendum Information',
    '689': 'File Header',
    '690': 'File Trailer',
    '691': 'Message Error Information',
    '692': 'File Rejection',
    '781': 'Fee Collection'
}


while offset < len(content):
    record = {"Record#": record_index}

    # RDW
    rdw_bytes, offset = read_bytes(content, offset, 4)
    rdw = int.from_bytes(rdw_bytes, byteorder="big")
    record["RDW"] = f"{rdw} x{rdw:08X}"

    # 读取报文数据
    message_bytes, offset = read_bytes(content, offset, rdw)
    message_offset = 0

    # MTI
    mti_bytes, message_offset = read_bytes(message_bytes, message_offset, 4)
    mti = mti_bytes.decode("ascii")
    record["MTI"] = mti

    # Bitmap1
    bitmap1, message_offset = read_bytes(message_bytes, message_offset, 8)
    bitmap1_hex = to_hex_str(bitmap1)
    record["Bitmap1"] = bitmap1_hex

    # Bit1 (Secondary Bitmap)
    if int(bitmap1_hex[0:2], 16) & (1 << 7):  # Check if the 1st bit (MSB of first byte) is set
        secondary_bitmap, message_offset = read_bytes(message_bytes, message_offset, 8)
        secondary_bitmap_hex = to_hex_str(secondary_bitmap)
        record["Bit1"] = secondary_bitmap_hex
    else:
        record["Bit1"] = ""

    # 所有存在的bit字段
    present_fields = parse_bitmap(bitmap1_hex, secondary_bitmap_hex)
    print("存在的字段编号：", present_fields)

    # 移除bit1
    present_fields.remove(1)

    # 处理 Bit24 (Function Code)
    bit24, message_offset = read_bytes(message_bytes, message_offset, 3)
    function_code = bit24.decode("ascii")
    record["Bit24"] = function_code + ':' + function_code_map.get(function_code, "Unknown")
    # 移除bit24
    present_fields.remove(24)

    # Bit33: 先读取2位长度，然后读取对应长度的数据
    bit33_len_bytes, message_offset = read_bytes(message_bytes, message_offset, 2)
    bit33_len = int(bit33_len_bytes.decode("ascii"))
    bit33_data, message_offset = read_bytes(message_bytes, message_offset, bit33_len)
    record["Bit33"] = bit33_len_bytes.decode("ascii") + ':' + bit33_data.decode("ascii")
    # 移除bit33
    present_fields.remove(33)

    # Bit48 解析
    bit48, message_offset = read_bytes(message_bytes, message_offset, 3)
    bit48_len = bit48.decode("ascii")
    record["Bit48"] = bit48_len
    # 移除bit48
    present_fields.remove(48)

    # PDE 长度动态解析，待解析总长度
    pde_len = int(bit48_len)
    pde_data, message_offset = read_bytes(message_bytes, message_offset, pde_len)
    pde_content = pde_data.decode("ascii")

    pos = 0
    # 解析PDE动态字段: 4位编号 + 3位长度 + 内容
    while pos + 7 <= len(pde_content):
        code = pde_content[pos:pos + 4]
        # 编号以30、35、39开头，且4位数字
        if not (code.isdigit() and code.startswith(('30', '35', '39'))):
            break
        length_str = pde_content[pos + 4:pos + 7]
        if not length_str.isdigit():
            break
        length = int(length_str)
        start = pos + 7
        end = start + length
        if end > len(pde_content):
            break
        pde_content_str = pde_content[start:end]
        record[f'PDE{code}'] = length_str + ':' + pde_content_str
        pos = end

    # PDE之后，解析剩余Bit字段
    if function_code in ['689', '690']:  # File Header + File Trailer # 固定解析 71,100
        # Bit71 解析
        bit71, message_offset = read_bytes(message_bytes, message_offset, 8)
        bit71_len = bit71.decode("ascii")
        record["Bit71"] = bit71_len
        # 移除bit71
        present_fields.remove(71)

        # Bit100 解析
        bit100_len, message_offset = read_bytes(message_bytes, message_offset, 2)
        bit100_len_str = bit100_len.decode("ascii")
        bit100_len_int = int(bit100_len_str)
        bit100, message_offset = read_bytes(message_bytes, message_offset, bit100_len_int)
        bit100_code = bit100.decode("ascii")
        record["Bit100"] = bit100_len_str + ':' + bit100_code
        # 移除bit100
        present_fields.remove(100)
    else:
        # 读取剩余的文件数据
        # 读取剩余的文件数据
        bit_offset = 0
        for field in present_fields:
            if field == 49:
                # Bit49: 固定长度3
                bit49, message_offset = read_bytes(message_bytes, message_offset, 3)
                bit49_len = bit49.decode("ascii")
                record["Bit49"] = bit49_len
            elif field == 71:
                # Bit71: 固定长度8
                bit71, message_offset = read_bytes(message_bytes, message_offset, 8)
                bit71_len = bit71.decode("ascii")
                record["Bit71"] = bit71_len
            elif field == 62 or (123 <= field <= 126):
                # Bit62, Bit123-126: 3位长度前缀 + 内容
                bit62_123_126_len, message_offset = read_bytes(message_bytes, message_offset, 3)
                bit62_123_126_len_str = bit62_123_126_len.decode("ascii")
                bit62_123_126_len_int = int(bit62_123_126_len_str)
                bit62_123_126, message_offset = read_bytes(message_bytes, message_offset, bit62_123_126_len_int)
                bit62_123_126_code = bit62_123_126.decode("ascii")
                record[f'Bit{field}'] = bit62_123_126_len_str + ':' + bit62_123_126_code
            elif field == 94 or field == 100:
                # Bit94, Bit100: 2位长度前缀 + 内容
                bit94_100_len, message_offset = read_bytes(message_bytes, message_offset, 2)
                bit94_100_len_str = bit94_100_len.decode("ascii")
                bit94_100_len_int = int(bit94_100_len_str)
                bit94_100, message_offset = read_bytes(message_bytes, message_offset, bit94_100_len_int)
                bit94_100_code = bit94_100.decode("ascii")
                record[f'Bit{field}'] = bit94_100_len_str + ':' + bit94_100_code
            else:
                # 其他字段不处理
                continue

    records.append(record)
    record_index += 1

# 转换为 DataFrame
df = pd.DataFrame(records)

# 生成带当前时间的文件名
current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
excel_path = f"src/excel/AcknowledgementFile_{current_time}.xlsx"

# 导出为 Excel
df.to_excel(excel_path, index=False)
print(f"Excel文件已保存到: {os.path.abspath(excel_path)}")
